/* eslint-disable padded-blocks */
/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
/* eslint-disable no-unsafe-optional-chaining */
/* eslint-disable import/order */
/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable no-unused-vars */
/* eslint-disable object-curly-newline */
import { Button, Card, Col, Pagination, Popconfirm, Row, Table, Typography } from 'antd';
import Search from 'antd/es/input/Search';
import React, { useEffect, useRef, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import useHttp from '../../../../hooks/use-http';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import ModalFormCreator from '../../../../component/common/ModalFormCreator';
import { apiGenerator, formatAmount, getMaxTimeUnit } from '../../../../util/functions';
import { useParams } from 'react-router-dom';
import { getAuthToken } from '../../../../util/API/authStorage';
import CRUDComponent from '../../../../component/common/CRUD-Component';

const { Text } = Typography;

const IPSettingPage = (props) => {

    const api = useHttp();
    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 10,
        total: 0,
    });
    const { projectID, serviceID, environmentID } = useParams();
    const [apiOverView, setApiOverView] = useState([]);
    const [searchValue, setSearchValue] = useState("");
    const [refresh, setRefresh] = useState(false);
    const [filterText, setFilterText] = useState("");
    const [methodQuery, setMethodQuery] = useState("");
    const [typeQuery, setTypeQuery] = useState("");
    const [extraQuery, setExtraQuery] = useState("");
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [IPSettingDetails, setIPSettingDetails] = useState({});
    const [IsIPSettingDetails, setIsIPSettingDetails] = useState(false);
    const [IsEndPointIPSettingDetails, setIsEndPointIPSettingDetails] = useState(false);
    const [environment, setEnvironment] = useState([]);

    const debounceTimeout = useRef(null);
    console.log(props, apiOverView);
    // useEffect(() => {
    //     const OVERVIEW_API = { ...CONSTANTS.API.rateLimitConfig.get };
    //     OVERVIEW_API.endpoint = OVERVIEW_API.endpoint.replace(
    //         ":serviceEnvironmentId",
    //         environmentID,
    //     );
    //     OVERVIEW_API.endpoint = `${OVERVIEW_API.endpoint}&page=${pagination.currentPage}&limit=${pagination.pageSize}`;

    //     if (filterText != null && filterText !== "") {
    //         const query = `&search=${filterText}`;
    //         OVERVIEW_API.endpoint += query;
    //     }
    //     OVERVIEW_API.endpoint = OVERVIEW_API.endpoint + extraQuery + methodQuery + typeQuery;
    //     api.sendRequest(OVERVIEW_API, (res) => {
    //         setPagination((prevPagination) => ({
    //             ...prevPagination,
    //             total: res?.data?.count ?? 90000,
    //         }));
    //         // totalEndpointCounts = res?.data?.count;
    //         setApiOverView(
    //             res?.data?.rows?.map((ele, i) => {
    //                 // const latency = getMaxTimeUnit(ele?.avgResponseTime);
    //                 return {
    //                     ...ele,
    //                     key: ele,
    //                     no:
    //                         pagination.currentPage === 1
    //                             ? i + 1
    //                             : pagination.pageSize * (pagination.currentPage - 1) + i + 1,
    //                     // totalCount: formatAmount(ele?.totalCount) || "0",
    //                     // successCount: (
    //                     //   <Text className="text-green-700">
    //                     //     {formatAmount(ele?.successCount) || "0"}
    //                     //     {' '}
    //                     //     (
    //                     //     {ele?.successCount && ele?.totalCount !== 0
    //                     //       ? ((ele?.successCount / ele?.totalCount) * 100)?.toFixed(0)
    //                     //       : "0"}
    //                     //     %)
    //                     //   </Text>
    //                     // ),
    //                     // failCount: (
    //                     //   <Text className="text-red-700">
    //                     //     {formatAmount(ele?.failCount) || "0"}
    //                     //     {' '}
    //                     //     (
    //                     //     {ele?.failCount && ele?.totalCount !== 0
    //                     //       ? ((ele?.failCount / ele?.totalCount) * 100)?.toFixed(0)
    //                     //       : "0"}
    //                     //     %)
    //                     //     {" "}
    //                     //   </Text>
    //                     // ),
    //                     // avgResponseTime: `${latency.time} ${latency.unit}`,
    //                     method: ele?.method || "-",
    //                     isBlockAfterFault: ele?.isBlockAfterFault ? "Yes" : "No",
    //                     shouldBlockIfNoKeyFound: ele?.shouldBlockIfNoKeyFound ? "Yes" : "No",
    //                     endpoint:
    //                         <p className="overflow-hidden break-all">{ele?.endpoint}</p>
    //                         || "-",

    //                 };
    //             }),
    //         );
    //     });
    // }, [
    //     filterText,
    //     extraQuery,
    //     methodQuery,
    //     typeQuery,
    //     pagination.currentPage,
    //     pagination.pageSize,
    //     refresh,
    //     // switchStates,
    //     environmentID,
    // ]);

    // Environment - dropdown
    useEffect(() => {
        if (getAuthToken() && environmentID) {
            api.sendRequest(
                apiGenerator(CONSTANTS.API.environment.getOneIP, {
                    dataId: environmentID,
                }),
                (res) => {
                    if (res?.status === "success") {
                        console.log(res?.data, "res");
                        setEnvironment(res?.data);
                    }
                },
            );
        }
    }, [serviceID]);
    const detials = [
        {
            id: 1,
            label: "Rate Limit",
            value: environment?.maxRequests || "-",
        },
        {
            id: 2,
            label: "Permanent Block",
            value: (
                <p className="overflow-hidden break-all">
                    {environment?.faultAllowLimit || "-"}
                </p>
            ),
        },
        {
            id: 3,
            label: "Duration",
            value: environment?.windowMs || "-",
        },
        {
            id: 4,
            label: "IP Key",
            value: environment?.ipKey || "-",
        },
        {
            id: 5,
            label: "Rate Limit Excesses Message",
            value: environment?.rateLimitExceedErrMsg ?? "-",
        },
        {
            id: 6,
            label: "Block IP Message",
            value: environment?.blockIpMsg || "-",
        },
        {
            id: 7,
            label: "Block after fault",
            value: environment?.isBlockAfterFault ? "Yea" : "No",
            type: "toggle",
        },
        {
            id: 8,
            label: "Block if no key found",
            // value: ,
            value: environment?.shouldBlockIfNoKeyFound ? "Yea" : "No",
            type: "toggle",
        },
        {
            id: 9,
            label: "Block Data Sync Interval",
            value: environment?.blockDataSyncInterval || "-",
        },
        {
            id: 10,
            label: "Auto release after",
            value: environment?.autoReleaseAfter || "-",
        },
    ];

    const handleTableChange = (filters, sorter) => {
        if (filters?.method) {
            setMethodQuery(`&method=${filters?.method?.toString()}`);
        } else {
            setMethodQuery("");
        }

        if (filters?.type) {
            setTypeQuery(`&type=${filters?.type?.toString()}`);
        } else {
            setTypeQuery("");
        }

        if (sorter?.order) {
            setExtraQuery(
                `&sort=${sorter?.columnKey}&sortBy=${sorter?.order === "descend" ? "DESC" : "ASC"
                }`,
            );
        } else {
            setExtraQuery("");
        }
    };

    const searchHandler = (value) => {
        // Clear the previous timeout if it exists
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        // Set a new timeout to call the debounced function after a certain delay
        debounceTimeout.current = setTimeout(() => {
            setPagination((prev) => ({ ...prev, currentPage: 1 }));
            setFilterText(value);
        }, 500); // Adjust the delay as needed
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: (value) => {
            // console.log(value);
            setSelectedRowKeys(value);
        },

    };
    const generalIPSettingHandler = (value) => {
        // console.log(value);
        const UPDATE_IP_SETTING_API = apiGenerator(CONSTANTS.API.environment.update, {
            dataId: serviceID,
        });
        let milliseconds = 0;

        // Convert to milliseconds based on selected unit
        switch (value?.durationUnit) {
            case 'sec':
                milliseconds = value?.windowMs * 1000;
                break;
            case 'min':
                milliseconds = value?.windowMs * 60 * 1000;
                break;
            case 'hour':
                milliseconds = value?.windowMs * 60 * 60 * 1000;
                break;
            default:
                milliseconds = value?.windowMs;
        }

        value.windowMs = milliseconds;
        delete value?.durationUnit;

        api.sendRequest(
            UPDATE_IP_SETTING_API,
            (res) => {
                console.log(res, "res");
                // setEditProjectDetails(null);
                setRefresh((prev) => !prev);
            },
            value,
            "Edit Setting Successfully!!!",
        );
    };
    return (
        <>
            <Row gutter={[16, 24]}>
                <Col span={24} lg={24} xl={24} xxl={24}>
                    <Card title={(
                        <div className="flex justify-between items-center">
                            <h3>General IP Setting</h3>
                            <div className="flex gap-3 items-center">
                                <Button
                                    className="btn-dashboard-icon textcolor"
                                    type="primary"
                                    // ghost
                                    // icon={<FaPlus size={13} />}
                                    // size="large"
                                    style={{ margin: "0px 5px" }}
                                    onClick={() => {
                                        setIsIPSettingDetails((pr) => !pr);
                                    }}
                                >
                                    Edit Endpoint Setting
                                </Button>
                            </div>
                        </div>
                    )}>
                        {detials?.map((ele) => (
                            <Row
                                gutter={[16, 16]}
                                className="mb-1.5 font-medium "
                                key={ele?.id}
                            >
                                <Col span={8}>
                                    {ele?.label}
                                    &nbsp;:
                                </Col>
                                <Col span={16}>{ele?.value}</Col>
                            </Row>
                        ))}
                        {/* <Button
                            className="my-4 px-4 textcolor"
                            type="primary"
                            onClick={() => {
                            }}
                        >
                            Change Service Details
                        </Button> */}
                    </Card>
                </Col>

                <Col span={24}>
                    <Card>
                        {/* <div className="flex items-center justify-between gap-3">
                            <Text className="hidden  md:block">Endpoint IP Setting</Text>
                            <div className="flex gap-3 items-center">
                                <Button
                                    className="btn-dashboard-icon textcolor"
                                    type="primary"
                                    // ghost
                                    icon={<FaPlus size={13} />}
                                    // size="large"
                                    style={{ margin: "0px 5px" }}
                                    onClick={() => {
                                        setIsEndPointIPSettingDetails((pr) => !pr);
                                    }}
                                >
                                    Add New Endpoint Setting
                                </Button>
                            </div>
                        </div>
                        <div className="mt-3">
                            <Row>
                                <Col span={24} lg={12} xl={8} xxl={8}>
                                    <Search
                                        width="auto"
                                        value={searchValue}
                                        // defaultValue={filterText}
                                        onChange={(e) => {
                                            searchHandler(e.target.value);
                                            setSearchValue(e.target.value);
                                        }}
                                        placeholder="Search by Endpoint name"
                                    />
                                </Col>
                            </Row>
                        </div> */}

                        {/* <div className="mt-5">
                            <Table
                                loading={api.isLoading}
                                dataSource={apiOverView}
                                columns={CONSTANTS.TABLE.IPSETTINGHEADER}
                                onChange={handleTableChange}
                                pagination={false}
                                scroll={{ x: 800, y: 1300 }}
                                rowSelection={{
                                    type: "checkbox",
                                    ...rowSelection,
                                }}
                            />
                            <Pagination
                                current={pagination?.currentPage}
                                pageSize={pagination?.pageSize}
                                total={pagination?.total}
                                className="mt-10"
                                showSizeChanger
                                onChange={(page, pageSize) => {
                                    setPagination((prev) => ({
                                        ...prev,
                                        currentPage: page,
                                        pageSize,
                                    }));
                                }}
                            />
                        </div> */}
                        <CRUDComponent
                            // reload={refresh}
                            GET={{
                                API: CONSTANTS.API.rateLimitConfig.get,
                                extraQuery: {
                                    serviceEnvironmentId: environmentID,
                                },
                                DataModifier: (res, API, Setrefresh) => {
                                    // console.log(notifyButton);
                                    console.log(res, "res");
                                    return res?.map((ele, i) => {
                                        return {
                                            ...ele,
                                            key: ele,
                                            no:
                                                ele?.id,
                                            method: ele?.method || "-",
                                            isBlockAfterFaultTag: ele?.isBlockAfterFault ? "Yes" : "No",
                                            shouldBlockIfNoKeyFoundTag: ele?.shouldBlockIfNoKeyFound ? "Yes" : "No",
                                            endpointTag:
                                                <p className="overflow-hidden break-all">{ele?.endpoint}</p>
                                                || "-",
                                            // deleteWithReason: <Button onClick={() => setDeleteOpen(el?.id)} type="primary" className="textcolor">
                                            //     <DeleteOutlined />
                                            // </Button>,
                                        };
                                    });
                                },
                                column: CONSTANTS.TABLE.IPSETTINGHEADER,
                            }}
                            UPDATE={{
                                API: CONSTANTS.API.rateLimitConfig.update,
                                message: "Updated Endpoint IP Setting Successfully",
                                modaltitle: "Update Endpoint IP Setting",
                                modalFields: "ENDPOINT_IP_SETTING_MODAL",
                                setRefresh,
                                // payloadModifier: (res) => res,
                            }}
                            CREATE={{
                                API: CONSTANTS.API.rateLimitConfig.add,
                                message: "Created Endpoint IP Setting Successfully",
                                modaltitle: "Add New Endpoint IP Setting",
                                modalFields: "ENDPOINT_IP_SETTING_MODAL",
                                name: "Add New Endpoint IP Setting",
                                setRefresh,
                                // payloadModifier: (res) => res,
                            }}
                            DELETE={{
                                API: CONSTANTS.API.rateLimitConfig.delete,
                                message: "Deleted Successfully",
                                setRefresh,
                            }}
                            isSearch
                        />
                    </Card>

                </Col>
            </Row>
            <ModalFormCreator
                open={IsIPSettingDetails}
                onCreate={generalIPSettingHandler}
                onCancel={() => {
                    setIsIPSettingDetails((pr) => !pr);
                    setIPSettingDetails({});
                }}
                name="Edit General IP Setting"
                menu="IP_SETTING_MODAL"
                formData={environment || {}}
            />
            <ModalFormCreator
                open={IsEndPointIPSettingDetails}
                onCreate={generalIPSettingHandler}
                onCancel={() => {
                    setIsEndPointIPSettingDetails((pr) => !pr);
                }}
                name="Add New Endpoint IP Setting"
                menu="ENDPOINT_IP_SETTING_MODAL"
            // formData={IPSettingDetails}
            />
        </>
    );
};

export default IPSettingPage;
