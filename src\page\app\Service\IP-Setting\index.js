/* eslint-disable react/jsx-closing-bracket-location */
/* eslint-disable no-unused-vars */
/* eslint-disable object-curly-newline */
import { <PERSON><PERSON>, Card, Col, Pagination, Popconfirm, Row, Table, Typography } from 'antd';
import Search from 'antd/es/input/Search';
import React, { useRef, useState } from 'react';
import { FaPlus } from 'react-icons/fa';
import useHttp from '../../../../hooks/use-http';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import ModalFormCreator from '../../../../component/common/ModalFormCreator';
import { apiGenerator } from '../../../../util/functions';

const { Text } = Typography;

const IPSettingPage = () => {
    const api = useHttp();
    const [pagination, setPagination] = useState({
        currentPage: 1,
        pageSize: 10,
        total: 0,
    });
    const [apiOverView, setApiOverView] = useState([]);
    const [searchValue, setSearchValue] = useState("");
    const [refresh, setRefresh] = useState(false);
    const [filterText, setFilterText] = useState("");
    const [methodQuery, setMethodQuery] = useState("");
    const [typeQuery, setTypeQuery] = useState("");
    const [extraQuery, setExtraQuery] = useState("");
    const [selectedRowKeys, setSelectedRowKeys] = useState([]);
    const [IPSettingDetails, setIPSettingDetails] = useState({});
    const [IsIPSettingDetails, setIsIPSettingDetails] = useState(false);
    const [IsEndPointIPSettingDetails, setIsEndPointIPSettingDetails] = useState(false);

    const debounceTimeout = useRef(null);
    const detials = [
        {
            id: 1,
            label: "Rate Limit",
            value: "1000" || "-",
        },
        {
            id: 2,
            label: "Permanent Block",
            value: (
                <p className="overflow-hidden break-all">
                    {"10" || "-"}
                </p>
            ),
        },
        {
            id: 3,
            label: "Duration",
            value: "15 min" || "-",
        },
        {
            id: 4,
            label: "Count",
            value: "100" || "-",
            // (serviceDetails?.createdAt
            //   && `${moment(serviceDetails?.createdAt)
            //     .utc()
            //     .format("MMM DD")} at ${moment
            //       .utc(serviceDetails?.createdAt)
            //       .local()
            //       .format("hh:mm:ss A")}`)
            // || "-",
        },
        {
            id: 5,
            label: "Rate Limit Excesses Message",
            value: "Rate limit exceeded" || "-",
        },
        {
            id: 5,
            label: "Block IP Message",
            value: "Rate limit exceeded" || "-",
        },
    ];

    const handleTableChange = (filters, sorter) => {
        if (filters?.method) {
            setMethodQuery(`&method=${filters?.method?.toString()}`);
        } else {
            setMethodQuery("");
        }

        if (filters?.type) {
            setTypeQuery(`&type=${filters?.type?.toString()}`);
        } else {
            setTypeQuery("");
        }

        if (sorter?.order) {
            setExtraQuery(
                `&sort=${sorter?.columnKey}&sortBy=${sorter?.order === "descend" ? "DESC" : "ASC"
                }`,
            );
        } else {
            setExtraQuery("");
        }
    };

    const searchHandler = (value) => {
        // Clear the previous timeout if it exists
        if (debounceTimeout.current) {
            clearTimeout(debounceTimeout.current);
        }

        // Set a new timeout to call the debounced function after a certain delay
        debounceTimeout.current = setTimeout(() => {
            setPagination((prev) => ({ ...prev, currentPage: 1 }));
            setFilterText(value);
        }, 500); // Adjust the delay as needed
    };
    const rowSelection = {
        selectedRowKeys,
        onChange: (value) => {
            // console.log(value);
            setSelectedRowKeys(value);
        },

    };
    const generalIPSettingHandler = (value) => {
        // console.log(value);
        // const UPDATE_IP_SETTING_API = apiGenerator(CONSTANTS.API.project.update, {
        //   dataId: projectId,
        // });

        // api.sendRequest(
        //   UPDATE_PROJECT_INFO_API,
        //   () => {
        //     setEditProjectDetails(null);
        //     setRefresh((prev) => !prev);
        //   },
        //   value,
        //   "Edit Details Successfully!!!",
        // );
    };
    return (
        <>
            <Row gutter={[16, 24]}>
                <Col span={24} lg={24} xl={24} xxl={24}>
                    <Card title={(
                        <div className="flex justify-between items-center">
                            <h3>General IP Setting</h3>
                            <div className="flex gap-3 items-center">
                                <Button
                                    className="btn-dashboard-icon textcolor"
                                    type="primary"
                                    // ghost
                                    // icon={<FaPlus size={13} />}
                                    // size="large"
                                    style={{ margin: "0px 5px" }}
                                    onClick={() => {
                                        setIsIPSettingDetails((pr) => !pr);
                                    }}
                                >
                                    Edit Endpoint Setting
                                </Button>
                            </div>
                        </div>
                    )}>
                        {detials?.map((ele) => (
                            <Row
                                gutter={[16, 16]}
                                className="mb-1.5 font-medium "
                                key={ele?.id}
                            >
                                <Col span={8}>
                                    {ele?.label}
                                    &nbsp;:
                                </Col>
                                <Col span={16}>{ele?.value}</Col>
                            </Row>
                        ))}
                        {/* <Button
                            className="my-4 px-4 textcolor"
                            type="primary"
                            onClick={() => {
                            }}
                        >
                            Change Service Details
                        </Button> */}
                    </Card>
                </Col>

                <Col span={24}>
                    <Card>
                        <div className="flex items-center justify-between gap-3">
                            <Text className="hidden  md:block">Endpoint IP Setting</Text>
                            <div className="flex gap-3 items-center">
                                <Button
                                    className="btn-dashboard-icon textcolor"
                                    type="primary"
                                    // ghost
                                    icon={<FaPlus size={13} />}
                                    // size="large"
                                    style={{ margin: "0px 5px" }}
                                    onClick={() => {
                                        setIsEndPointIPSettingDetails((pr) => !pr);
                                    }}
                                >
                                    Add New Endpoint Setting
                                </Button>
                            </div>
                        </div>
                        <div className="mt-3">
                            <Row>
                                <Col span={24} lg={12} xl={8} xxl={8}>
                                    <Search
                                        width="auto"
                                        value={searchValue}
                                        // defaultValue={filterText}
                                        onChange={(e) => {
                                            searchHandler(e.target.value);
                                            setSearchValue(e.target.value);
                                        }}
                                        placeholder="Search by Endpoint name"
                                    />
                                </Col>
                            </Row>
                        </div>

                        <div className="mt-5">
                            <Table
                                loading={api.isLoading}
                                dataSource={apiOverView}
                                columns={CONSTANTS.TAB_MENU.IPSETTINGHEADER}
                                onChange={handleTableChange}
                                pagination={false}
                                scroll={{ x: 800, y: 1300 }}
                                rowSelection={{
                                    type: "checkbox",
                                    ...rowSelection,
                                }}
                            />
                            <Pagination
                                current={pagination?.currentPage}
                                pageSize={pagination?.pageSize}
                                total={pagination?.total}
                                className="mt-10"
                                showSizeChanger
                                onChange={(page, pageSize) => {
                                    setPagination((prev) => ({
                                        ...prev,
                                        currentPage: page,
                                        pageSize,
                                    }));
                                }}
                            />
                        </div>
                    </Card>

                </Col>
            </Row>
            <ModalFormCreator
                open={IsIPSettingDetails}
                onCreate={generalIPSettingHandler}
                onCancel={() => {
                    setIsIPSettingDetails((pr) => !pr);
                    setIPSettingDetails({});
                }}
                name="Edit General IP Setting"
                menu="IP_SETTING_MODAL"
                formData={{}}
            />
            <ModalFormCreator
                open={IsEndPointIPSettingDetails}
                onCreate={generalIPSettingHandler}
                onCancel={() => {
                    setIsEndPointIPSettingDetails((pr) => !pr);
                }}
                name="Add New Endpoint IP Setting"
                menu="ENDPOINT_IP_SETTING_MODAL"
            // formData={IPSettingDetails}
            />
        </>
    );
};

export default IPSettingPage;
