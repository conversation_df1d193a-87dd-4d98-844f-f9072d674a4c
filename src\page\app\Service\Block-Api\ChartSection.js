import { Pie } from '@ant-design/plots';
import { Card, Col, Row } from 'antd';
import { useParams } from 'react-router-dom';
import React, { useEffect, useState } from 'react';
import { apiGenerator } from '../../../../util/functions';
import CONSTANTS from '../../../../util/constant/CONSTANTS';
import useHttp from '../../../../hooks/use-http';

const chartColor = [
    '#FF6B35',
    '#F7931E',
    '#FFB366',
    '#FFCC99',
    '#FF9999',
    '#FFD700',
];
const ChartSection = () => {
    // Calculate chart data from actual blocked IP statistics
    const { environmentID } = useParams();
    const api = useHttp();
    const [chartAnalysis, setChartAnalysis] = useState([]);

    useEffect(() => {
        api.sendRequest(apiGenerator(CONSTANTS.API.blockIP.getAnalytic, {
            serviceEnvironmentId: environmentID,
        }), (res) => {
            setChartAnalysis(res?.data);
        });
    }, []);
    const calculateChartData = () => {
        const methodCounts = {};
        const total = chartAnalysis?.length;

        // Count methods
        chartAnalysis?.forEach((item) => {
            const method = item.agent || 'Unknown';
            methodCounts[method] = (methodCounts[method] || 0) + 1;
        });

        // Convert to chart data with colors
        // const colors = ['#FF6B35', '#F7931E', '#FFB366', '#FFCC99', '#FF9999', '#FFD700'];
        const chartData = Object.entries(methodCounts)?.map(([method, count], index) => ({
            type: `${method}`,
            value: total > 0 ? Math.round((count / total) * 100) : 0,
            count,
            color: chartColor[index % chartColor.length],
        }));

        // If no data, show dummy data
        if (chartData.length === 0) {
            return chartAnalysis?.map((res, i) => {
                return {
                    type: `${res?.agent}`,
                    value: res?.totalExceedCount,
                    count: 0,
                    color: chartColor[i],
                };
            });
            // [
            //     {
            //         type: 'GET Requests',
            //         value: 40,
            //         count: 0,
            //         color: '#FF6B35',
            //     },
            //     {
            //         type: 'POST Requests',
            //         value: 25,
            //         count: 0,
            //         color: '#F7931E',
            //     },
            //     {
            //         type: 'PUT Requests',
            //         value: 19,
            //         count: 0,
            //         color: '#FFB366',
            //     },
            //     {
            //         type: 'DELETE Requests',
            //         value: 16,
            //         count: 0,
            //         color: '#FFCC99',
            //     },
            // ];
        }

        return chartData;
    };

    const chartData = calculateChartData();

    const config = {
        appendPadding: 10,
        data: chartData,
        angleField: 'value',
        colorField: 'color',
        radius: 1,
        innerRadius: 0.6,
        label: {
            type: 'inner',
            offset: '-50%',
            content: '{value}%',
            style: {
                textAlign: 'center',
                fontSize: 14,
                fontWeight: 'bold',
                fill: '#fff',
            },
        },
        color: ({ color }) => {
            // const colorMap = {
            //     'GET Requests': '#FF6B35',
            //     'POST Requests': '#F7931E',
            //     'PUT Requests': '#FFB366',
            //     'DELETE Requests': '#FFCC99',
            // };
            return color;
        },
        interactions: [
            {
                type: 'element-selected',
            },
            {
                type: 'element-active',
            },
        ],
        statistic: {
            title: false,
            content: {
                style: {
                    whiteSpace: 'pre-wrap',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    color: '#666',
                },
                content: 'Blocked\nRequests',
            },
        },
        legend: false,
    };
    return (
        <>
            <Card title="Blocked IP Statistics Overview">
                <Row gutter={[24, 24]}>
                    <Col xs={24} sm={24} md={12} lg={10} xl={8}>
                        <div style={{ height: '300px' }}>
                            <Pie {...config} />
                        </div>
                    </Col>
                    <Col xs={24} sm={24} md={12} lg={14} xl={16}>
                        <div className="flex flex-wrap gap-4">
                            <Row gutter={[16, 16]} className="w-full">
                                {chartData.map((item) => (
                                    <Col xs={12} sm={12} md={12} lg={6} xl={6} key={item.type}>
                                        <div className="flex items-center gap-3 p-3 border rounded-lg">
                                            <div
                                                className="w-4 h-4 rounded-full"
                                                style={{ backgroundColor: item.color }}
                                            />
                                            <div>
                                                <div className="text-lg font-bold text-gray-800">
                                                    {item.value}
                                                    %
                                                </div>
                                                <div className="text-sm text-gray-600">
                                                    {item.type}
                                                </div>
                                                {/* <div className="text-xs text-gray-500">
                                                    {item.count}
                                                    {' '}
                                                    endpoints
                                                </div> */}
                                            </div>
                                        </div>
                                    </Col>
                                ))}
                            </Row>
                        </div>
                    </Col>
                </Row>
            </Card>
        </>
    );
};

export default ChartSection;
