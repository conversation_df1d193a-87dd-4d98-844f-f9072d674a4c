/* eslint-disable no-param-reassign */
/* eslint-disable no-unused-vars */
import {
  Button, Card, Col, Row,
  Switch,
  Tooltip,
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import ModalFormCreator from '../../../../component/common/ModalFormCreator';
import { getAuthToken } from '../../../../util/API/authStorage';
import { apiGenerator, convertToMiliseconds } from '../../../../util/functions';
import useHttp from '../../../../hooks/use-http';
import CONSTANTS from '../../../../util/constant/CONSTANTS';

const GeneralIPCard = () => {
  const { projectID, serviceID, environmentID } = useParams();
  const [environment, setEnvironment] = useState([]);
  const [IsIPSettingDetails, setIsIPSettingDetails] = useState(false);
  const api = useHttp();
  useEffect(() => {
    if (getAuthToken() && environmentID) {
      api.sendRequest(
        apiGenerator(CONSTANTS.API.environment.getOneIP, {
          dataId: environmentID,
        }),
        (res) => {
          if (res?.status === "success") {
            setEnvironment(res?.data);
          }
        },
      );
    }
  }, [serviceID]);
  const generalIPSettingHandler = (value) => {
    const UPDATE_IP_SETTING_API = apiGenerator(CONSTANTS.API.environment.update, {
      dataId: environmentID,
    });
    // let milliseconds = 0;

    // Convert to milliseconds based on selected unit
    if (value?.windowMs) {
      value.windowMs = convertToMiliseconds(value?.windowMs, value?.durationUnit);
      delete value?.durationUnit;
    }
    api.sendRequest(
      UPDATE_IP_SETTING_API,
      (res) => {
        setEnvironment((pr) => ({ ...pr, ...value }));
        setIsIPSettingDetails(false);
      },
      value,
      "Edit Setting Successfully!!!",
    );
  };
  const detials = [
    {
      id: 1,
      label: "Rate Limit",
      value: environment?.maxRequests || "-",
    },
    {
      id: 2,
      label: "Permanent Block",
      value: (
        <p className="overflow-hidden break-all">
          {environment?.faultAllowLimit || "-"}
        </p>
      ),
    },
    {
      id: 3,
      label: "Duration",
      value: environment?.windowMs || "-",
    },
    {
      id: 4,
      label: "IP Key",
      value: environment?.ipKey || "-",
    },
    {
      id: 5,
      label: "Rate Limit Excesses Message",
      value: environment?.rateLimitExceedErrMsg ?? "-",
    },
    {
      id: 6,
      label: "Block IP Message",
      value: environment?.blockIpMsg || "-",
    },
    {
      id: 7,
      label: "Block after fault",
      value: environment?.isBlockAfterFault ? "Yes" : "No",
      type: "toggle",
    },
    {
      id: 8,
      label: "Block if no key found",
      value: environment?.shouldBlockIfNoKeyFound ? "Yes" : "No",
      type: "toggle",
    },
    // {
    //   id: 9,
    //   label: "Is Rate Limit",
    //   value: environment?.isRateLimit ? "Yes" : "No",
    //   type: "toggle",
    // },
    {
      id: 10,
      label: "Block Data Sync Interval",
      value: environment?.blockDataSyncInterval || "-",
    },
    {
      id: 11,
      label: "Auto release after",
      value: environment?.autoReleaseAfter || "-",
    },
  ];
  return (
    <>
      <Col span={24} lg={24} xl={24} xxl={24}>
        <Card title={(
          <div className="flex justify-between items-center">
            <h3>General IP Setting</h3>
            <div className="flex gap-3 items-center">
              <Tooltip title="Rate Limit">
                <Switch
                  checkedChildren="Enable"
                  unCheckedChildren="Disable"
                  loading={api.isLoading}
                  checked={environment?.isRateLimit || false}
                  onChange={(value) => {
                    generalIPSettingHandler({
                      isRateLimit: value,
                    });
                  }}
                />
              </Tooltip>
              <Button
                className="btn-dashboard-icon textcolor"
                type="primary"
                style={{ margin: "0px 5px" }}
                onClick={() => {
                  setIsIPSettingDetails(true);
                }}
              >
                Edit Endpoint Setting
              </Button>
            </div>
          </div>
        )}
        >
          {detials?.map((ele) => (
            <Row
              gutter={[16, 16]}
              className="mb-1.5 font-medium "
              key={ele?.id}
            >
              <Col span={8}>
                {ele?.label}
                &nbsp;:
              </Col>
              <Col span={16}>{ele?.value}</Col>
            </Row>
          ))}
        </Card>
      </Col>
      <ModalFormCreator
        open={IsIPSettingDetails}
        onCreate={generalIPSettingHandler}
        onCancel={() => {
          setIsIPSettingDetails((pr) => !pr);
        }}
        name="Edit General IP Setting"
        menu="IP_SETTING_MODAL"
        formData={environment || {}}
      />
    </>
  );
};

export default GeneralIPCard;
