# Duration Field with Millisecond Conversion

## Overview
The enhanced duration field provides automatic conversion from time units (seconds, minutes, hours) to milliseconds. This is useful for API integrations that require time values in milliseconds while providing a user-friendly interface.

## Features
- **Time Unit Selection**: Choose between seconds, minutes, and hours
- **Automatic Conversion**: Real-time conversion to milliseconds
- **Callback Support**: Get notified when values change
- **Flexible Configuration**: Customizable options and default values

## Usage

### Basic Configuration
```javascript
const durationFieldConfig = {
    type: "duration",
    name: "duration",
    id: "duration",
    Label: "Duration",
    placeholder: "Enter duration",
    required: true,
    defaultUnit: "seconds", // Default unit selection
    onUnitChange: (milliseconds, unit, originalValue) => {
        // Handle the conversion
        console.log(`${originalValue} ${unit} = ${milliseconds} ms`);
    }
};
```

### Advanced Configuration
```javascript
const advancedDurationConfig = {
    type: "duration",
    name: "timeout",
    id: "timeout",
    Label: "Request Timeout",
    placeholder: "Enter timeout duration",
    required: true,
    min: 1,
    defaultUnit: "minutes",
    unitFieldName: "timeoutUnit", // Store unit selection separately
    
    // Custom unit options
    options: [
        { label: 'sec', value: 'seconds' },
        { label: 'min', value: 'minutes' },
        { label: 'hour', value: 'hours' }
    ],
    
    // Callback for handling conversions
    onUnitChange: (milliseconds, unit, originalValue) => {
        // Update your state or send to API
        setApiTimeout(milliseconds);
        
        // Log for debugging
        console.log(`Timeout set to: ${originalValue} ${unit} (${milliseconds} ms)`);
    }
};
```

## Conversion Formulas

| Unit | Formula | Example |
|------|---------|---------|
| Seconds | `value × 1000` | 30 sec = 30,000 ms |
| Minutes | `value × 60 × 1000` | 5 min = 300,000 ms |
| Hours | `value × 60 × 60 × 1000` | 2 hours = 7,200,000 ms |

## Props

| Prop | Type | Required | Default | Description |
|------|------|----------|---------|-------------|
| `type` | string | Yes | - | Must be "duration" |
| `name` | string | Yes | - | Form field name |
| `id` | string | Yes | - | Field identifier |
| `Label` | string | Yes | - | Field label |
| `defaultUnit` | string | No | "seconds" | Default unit selection |
| `unitFieldName` | string | No | - | Separate field to store unit |
| `onUnitChange` | function | No | - | Callback for value changes |
| `options` | array | No | Default units | Custom unit options |
| `min` | number | No | - | Minimum value |
| `required` | boolean | No | false | Field validation |
| `disabled` | boolean | No | false | Disable field |

## Callback Function

The `onUnitChange` callback receives three parameters:

```javascript
onUnitChange: (milliseconds, unit, originalValue) => {
    // milliseconds: converted value in milliseconds
    // unit: selected time unit ('seconds', 'minutes', 'hours')
    // originalValue: user input value
}
```

## Integration Examples

### Rate Limiting Configuration
```javascript
const rateLimitConfig = {
    type: "duration",
    name: "windowDuration",
    Label: "Rate Limit Window",
    defaultUnit: "minutes",
    onUnitChange: (ms) => {
        updateRateLimitSettings({ windowMs: ms });
    }
};
```

### API Timeout Settings
```javascript
const timeoutConfig = {
    type: "duration", 
    name: "requestTimeout",
    Label: "Request Timeout",
    defaultUnit: "seconds",
    min: 1,
    onUnitChange: (ms) => {
        setApiConfig(prev => ({ ...prev, timeout: ms }));
    }
};
```

### Cache Expiration
```javascript
const cacheConfig = {
    type: "duration",
    name: "cacheExpiry", 
    Label: "Cache Expiration",
    defaultUnit: "hours",
    onUnitChange: (ms) => {
        setCacheSettings({ ttl: ms });
    }
};
```

## Best Practices

1. **Choose Appropriate Default Units**: Use the most common unit for your use case
2. **Validate Ranges**: Set appropriate min/max values for your context
3. **Handle Edge Cases**: Consider zero values and very large numbers
4. **Provide Clear Labels**: Make it obvious what the duration controls
5. **Use Callbacks Wisely**: Update your application state when values change

## Common Use Cases

- API request timeouts
- Rate limiting windows
- Cache expiration times
- Session durations
- Retry intervals
- Polling frequencies
- Animation durations
