import React, { useState } from 'react';
import { Form, Card, Typography, Space } from 'antd';
import FormFields from '../common/FormFields';

const { Text, Title } = Typography;

const DurationFieldExample = () => {
    const [form] = Form.useForm();
    const [milliseconds, setMilliseconds] = useState(0);
    const [currentUnit, setCurrentUnit] = useState('seconds');
    const [currentValue, setCurrentValue] = useState(0);

    const durationFieldConfig = {
        type: "duration",
        name: "duration",
        id: "duration",
        Label: "Duration",
        placeholder: "Enter duration",
        required: true,
        defaultUnit: "seconds",
        unitFieldName: "durationUnit", // Optional: to store unit selection
        onUnitChange: (ms, unit, value) => {
            setMilliseconds(ms);
            setCurrentUnit(unit);
            setCurrentValue(value);
            console.log(`Duration: ${value} ${unit} = ${ms} milliseconds`);
        },
        // Custom options (optional - will use default if not provided)
        options: [
            { label: 'sec', value: 'seconds' },
            { label: 'min', value: 'minutes' },
            { label: 'hour', value: 'hours' }
        ]
    };

    return (
        <Card title="Duration Field with Millisecond Conversion Example">
            <Form form={form} layout="vertical">
                <FormFields 
                    data={durationFieldConfig} 
                    form={form}
                    formData={{}}
                />
                
                <Space direction="vertical" style={{ marginTop: 16 }}>
                    <Title level={5}>Conversion Results:</Title>
                    <Text>Current Value: <strong>{currentValue} {currentUnit}</strong></Text>
                    <Text>Converted to Milliseconds: <strong>{milliseconds} ms</strong></Text>
                    
                    <div style={{ marginTop: 16, padding: 12, backgroundColor: '#f5f5f5', borderRadius: 4 }}>
                        <Text strong>Conversion Formula:</Text>
                        <br />
                        <Text code>
                            {currentUnit === 'seconds' && `${currentValue} seconds × 1000 = ${milliseconds} ms`}
                            {currentUnit === 'minutes' && `${currentValue} minutes × 60 × 1000 = ${milliseconds} ms`}
                            {currentUnit === 'hours' && `${currentValue} hours × 60 × 60 × 1000 = ${milliseconds} ms`}
                        </Text>
                    </div>
                </Space>
            </Form>
        </Card>
    );
};

export default DurationFieldExample;
